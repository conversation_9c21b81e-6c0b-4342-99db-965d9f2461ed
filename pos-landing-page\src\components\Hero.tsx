import React from 'react'
import { motion } from 'framer-motion'
import { WifiOff, Database, HardDrive, Shield } from 'lucide-react'
import { Button } from '@/components/ui/button'
import FeatureIcon from '@/components/ui/feature-icon'
import { scrollToSection } from '@/lib/utils'

const Hero: React.FC = () => {
  const stats = [
    { icon: WifiOff, label: 'Works Offline', value: '100%' },
    { icon: Database, label: 'Local Storage', value: 'SQLite' },
    { icon: HardDrive, label: 'Cloud Backup', value: 'Auto' },
    { icon: Shield, label: 'Multi-Platform', value: 'Ready' },
  ]

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Dark Gray Background */}
      <div className="absolute inset-0 bg-gray-900"></div>

      {/* Subtle Grid Pattern Overlay */}
      <div className="absolute inset-0 opacity-5" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }}></div>

      {/* Gradient Overlay for Depth */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-800/50 via-gray-900/80 to-black/60"></div>

      {/* Floating TT Image with Animation */}
      <motion.div
        initial={{ opacity: 0, y: -100, scale: 0.5 }}
        animate={{
          opacity: 1,
          y: 0,
          scale: 1,
          rotate: [0, 5, -5, 0]
        }}
        transition={{
          duration: 1.2,
          delay: 0.3,
          rotate: {
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }
        }}
        className="absolute top-8 left-1/2 transform -translate-x-1/2 z-20"
      >
        <div className="relative">
          {/* Glow Effect */}
          <div className="absolute inset-0 bg-green-400/30 rounded-full blur-xl scale-150 animate-pulse"></div>

          {/* Main Image */}
          <motion.img
            src="/TT.png"
            alt="TT Logo"
            className="relative z-10 w-24 h-24 sm:w-32 sm:h-32 lg:w-40 lg:h-40 object-contain drop-shadow-2xl"
            whileHover={{
              scale: 1.1,
              rotate: 360,
              transition: { duration: 0.8 }
            }}
            animate={{
              y: [0, -10, 0],
            }}
            transition={{
              y: {
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }
            }}
          />

          {/* Sparkle Effects */}
          <motion.div
            className="absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full"
            animate={{
              scale: [0, 1, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: 0.5
            }}
          />
          <motion.div
            className="absolute -bottom-2 -left-2 w-3 h-3 bg-blue-400 rounded-full"
            animate={{
              scale: [0, 1, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: 1
            }}
          />
          <motion.div
            className="absolute top-1/2 -right-4 w-2 h-2 bg-green-400 rounded-full"
            animate={{
              scale: [0, 1, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: 1.5
            }}
          />
        </div>
      </motion.div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center lg:text-left"
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="inline-flex items-center px-6 py-3 rounded-full bg-white/20 backdrop-blur-sm border border-white/30 text-white text-sm font-medium mb-8"
            >
              <span className="w-2 h-2 bg-white rounded-full mr-3 animate-pulse" />
              🌐 Works 100% Offline - No Internet Required
            </motion.div>

            {/* Main Heading */}
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-4xl sm:text-5xl lg:text-7xl font-bold text-white leading-tight mb-8"
            >
              Professional
              <span className="text-white"> POS System</span>
              <br />
              <span className="text-3xl sm:text-4xl lg:text-5xl text-green-100">for Modern Business</span>
            </motion.h1>

            {/* Description */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-xl text-green-100 mb-8 max-w-2xl mx-auto lg:mx-0 leading-relaxed"
            >
              Advanced Flutter-based point of sale solution that works completely offline.
              Features partial payments, multi-language support, thermal printer integration,
              and comprehensive inventory management. Built for businesses that demand excellence.
            </motion.p>

            {/* Offline Features Highlight */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.45 }}
              className="mb-10"
            >
              <div className="bg-white/15 backdrop-blur-md rounded-3xl p-8 border-2 border-white/30 shadow-2xl">
                <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                  <span className="w-4 h-4 bg-white rounded-full mr-4 animate-pulse"></span>
                  🌐 Complete Offline Functionality
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-green-100">
                  <div className="flex items-start">
                    <div className="mr-4 mt-1 flex-shrink-0">
                      <FeatureIcon
                        iconName="backup-icon"
                        alt="Local & Cloud Backup"
                        className="w-10 h-10"
                        fallbackEmoji="💾"
                      />
                    </div>
                    <div>
                      <strong className="text-white text-base">Local & Cloud Backup</strong>
                      <p className="text-sm text-green-100 mt-1">Automatic SQLite backup with Google Drive sync</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="mr-4 mt-1 flex-shrink-0">
                      <FeatureIcon
                        iconName="barcode-icon"
                        alt="Barcode Generation & Printing"
                        className="w-10 h-10"
                        fallbackEmoji="📊"
                      />
                    </div>
                    <div>
                      <strong className="text-white text-base">Barcode Generation & Printing</strong>
                      <p className="text-sm text-green-100 mt-1">Create and print barcodes for all products</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="mr-4 mt-1 flex-shrink-0">
                      <FeatureIcon
                        iconName="thermal-printer-icon"
                        alt="Thermal Receipt Printing"
                        className="w-10 h-10"
                        fallbackEmoji="🖨️"
                      />
                    </div>
                    <div>
                      <strong className="text-white text-base">Thermal Receipt Printing</strong>
                      <p className="text-sm text-green-100 mt-1">58mm/80mm thermal printer support via Bluetooth</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="mr-4 mt-1 flex-shrink-0">
                      <FeatureIcon
                        iconName="cross-platform-icon"
                        alt="Cross-Platform Sync"
                        className="w-10 h-10"
                        fallbackEmoji="📱"
                      />
                    </div>
                    <div>
                      <strong className="text-white text-base">Cross-Platform Sync</strong>
                      <p className="text-sm text-green-100 mt-1">Works on mobile, tablet, and desktop seamlessly</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="mr-4 mt-1 flex-shrink-0">
                      <FeatureIcon
                        iconName="secure-storage-icon"
                        alt="Secure Local Storage"
                        className="w-10 h-10"
                        fallbackEmoji="🔒"
                      />
                    </div>
                    <div>
                      <strong className="text-white text-base">Secure Local Storage</strong>
                      <p className="text-sm text-green-100 mt-1">Encrypted SQLite database with data protection</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="mr-4 mt-1 flex-shrink-0">
                      <FeatureIcon
                        iconName="real-time-icon"
                        alt="Real-Time Processing"
                        className="w-10 h-10"
                        fallbackEmoji="⚡"
                      />
                    </div>
                    <div>
                      <strong className="text-white text-base">Real-Time Processing</strong>
                      <p className="text-sm text-green-100 mt-1">Lightning-fast transactions without internet delays</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start mb-16"
            >
              <Button
                size="xl"
                onClick={() => scrollToSection('features')}
                className="bg-green-500 text-white hover:bg-green-600 shadow-2xl hover:shadow-green-500/25 transition-all duration-300 transform hover:scale-105 border-2 border-green-500 hover:border-green-600 font-bold px-12 py-6 text-xl"
              >
                Explore Offline Features
              </Button>
              <Button
                size="xl"
                onClick={() => window.open('http://localhost:8080', '_blank')}
                className="bg-gray-700 hover:bg-gray-600 border-2 border-gray-600 hover:border-gray-500 text-white transition-all duration-300 hover:scale-105 shadow-xl hover:shadow-gray-700/30 font-bold px-12 py-6 text-xl"
              >
                Try Live Demo
              </Button>
            </motion.div>

            {/* Professional Stats Cards */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
            >
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.7 + index * 0.1 }}
                  className="group"
                >
                  <div className="relative bg-gradient-to-br from-white/25 to-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/30 hover:border-white/50 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-white/20 min-h-[160px]">
                    {/* Background Glow Effect */}
                    <div className="absolute inset-0 bg-gradient-to-br from-green-400/10 to-blue-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    {/* Content */}
                    <div className="relative z-10 flex flex-col items-center text-center h-full justify-between">
                      {/* Icon Container */}
                      <div className="w-16 h-16 bg-gradient-to-br from-white/40 to-white/20 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                        <stat.icon className="w-8 h-8 text-white drop-shadow-lg" />
                      </div>

                      {/* Value */}
                      <div className="my-3">
                        <span className="text-4xl font-black text-white stats-counter tracking-tight">
                          {stat.value}
                        </span>
                      </div>

                      {/* Label */}
                      <p className="text-sm font-semibold text-white/90 uppercase tracking-wide leading-tight">
                        {stat.label}
                      </p>
                    </div>

                    {/* Decorative Elements */}
                    <div className="absolute top-3 right-3 w-2 h-2 bg-green-400 rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="absolute bottom-3 left-3 w-1 h-1 bg-white/60 rounded-full"></div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Three TT Images */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative flex justify-center lg:justify-end"
          >
            <div className="relative flex items-center justify-center gap-8">
              {/* TT Image 1 - Left */}
              <motion.div
                initial={{ opacity: 0, x: -30, y: 20 }}
                animate={{ opacity: 1, x: 0, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="relative z-10 transform -rotate-12 hover:rotate-0 transition-transform duration-500"
              >
                <div className="relative">
                  {/* Glow Effect */}
                  <div className="absolute inset-0 bg-green-400/40 rounded-full blur-2xl scale-150 animate-pulse"></div>

                  {/* Main Image */}
                  <motion.img
                    src="/TT.png"
                    alt="TT Logo 1"
                    className="relative z-10 w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48 object-contain drop-shadow-2xl"
                    whileHover={{
                      scale: 1.2,
                      rotate: 360,
                      transition: { duration: 0.8 }
                    }}
                    animate={{
                      y: [0, -15, 0],
                      rotate: [0, 10, -10, 0]
                    }}
                    transition={{
                      y: {
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      },
                      rotate: {
                        duration: 4,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }
                    }}
                  />

                  {/* Sparkle Effects */}
                  <motion.div
                    className="absolute -top-4 -right-4 w-6 h-6 bg-yellow-400 rounded-full"
                    animate={{
                      scale: [0, 1, 0],
                      opacity: [0, 1, 0],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: 0.5
                    }}
                  />
                  <motion.div
                    className="absolute -bottom-4 -left-4 w-4 h-4 bg-blue-400 rounded-full"
                    animate={{
                      scale: [0, 1, 0],
                      opacity: [0, 1, 0],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: 1
                    }}
                  />
                </div>
              </motion.div>

              {/* TT Image 2 - Center (Larger) */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="relative z-20 transform hover:scale-110 transition-transform duration-500"
              >
                <div className="relative">
                  {/* Enhanced Glow Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-green-400/50 to-blue-400/50 rounded-full blur-3xl scale-150 animate-pulse"></div>

                  {/* Main Image - Larger */}
                  <motion.img
                    src="/TT.png"
                    alt="TT Logo 2"
                    className="relative z-10 w-40 h-40 sm:w-52 sm:h-52 lg:w-64 lg:h-64 object-contain drop-shadow-2xl"
                    whileHover={{
                      scale: 1.15,
                      rotate: -360,
                      transition: { duration: 1 }
                    }}
                    animate={{
                      y: [0, -20, 0],
                      scale: [1, 1.05, 1]
                    }}
                    transition={{
                      y: {
                        duration: 4,
                        repeat: Infinity,
                        ease: "easeInOut"
                      },
                      scale: {
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }
                    }}
                  />

                  {/* Enhanced Sparkle Effects */}
                  <motion.div
                    className="absolute -top-6 -right-6 w-8 h-8 bg-green-400 rounded-full"
                    animate={{
                      scale: [0, 1, 0],
                      opacity: [0, 1, 0],
                      rotate: [0, 360]
                    }}
                    transition={{
                      duration: 2.5,
                      repeat: Infinity,
                      delay: 0.3
                    }}
                  />
                  <motion.div
                    className="absolute -bottom-6 -left-6 w-6 h-6 bg-purple-400 rounded-full"
                    animate={{
                      scale: [0, 1, 0],
                      opacity: [0, 1, 0],
                      rotate: [0, -360]
                    }}
                    transition={{
                      duration: 2.5,
                      repeat: Infinity,
                      delay: 0.8
                    }}
                  />
                  <motion.div
                    className="absolute top-1/2 -right-8 w-4 h-4 bg-yellow-400 rounded-full"
                    animate={{
                      scale: [0, 1, 0],
                      opacity: [0, 1, 0],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: 1.3
                    }}
                  />
                </div>
              </motion.div>

              {/* TT Image 3 - Right */}
              <motion.div
                initial={{ opacity: 0, x: 30, y: 20 }}
                animate={{ opacity: 1, x: 0, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                className="relative z-10 transform rotate-12 hover:rotate-0 transition-transform duration-500"
              >
                <div className="relative">
                  {/* Glow Effect */}
                  <div className="absolute inset-0 bg-blue-400/40 rounded-full blur-2xl scale-150 animate-pulse"></div>

                  {/* Main Image */}
                  <motion.img
                    src="/TT.png"
                    alt="TT Logo 3"
                    className="relative z-10 w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48 object-contain drop-shadow-2xl"
                    whileHover={{
                      scale: 1.2,
                      rotate: -360,
                      transition: { duration: 0.8 }
                    }}
                    animate={{
                      y: [0, -15, 0],
                      rotate: [0, -10, 10, 0]
                    }}
                    transition={{
                      y: {
                        duration: 3.5,
                        repeat: Infinity,
                        ease: "easeInOut"
                      },
                      rotate: {
                        duration: 4.5,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }
                    }}
                  />

                  {/* Sparkle Effects */}
                  <motion.div
                    className="absolute -top-4 -left-4 w-5 h-5 bg-green-400 rounded-full"
                    animate={{
                      scale: [0, 1, 0],
                      opacity: [0, 1, 0],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: 0.7
                    }}
                  />
                  <motion.div
                    className="absolute -bottom-4 -right-4 w-4 h-4 bg-red-400 rounded-full"
                    animate={{
                      scale: [0, 1, 0],
                      opacity: [0, 1, 0],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: 1.2
                    }}
                  />
                </div>
              </motion.div>

              {/* Enhanced Floating Decorative Elements */}
              <motion.div
                className="absolute -top-12 left-1/4 w-8 h-8 bg-green-400 rounded-full opacity-70"
                animate={{
                  y: [0, -20, 0],
                  scale: [1, 1.2, 1],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  delay: 0
                }}
              />
              <motion.div
                className="absolute -bottom-12 right-1/4 w-6 h-6 bg-blue-400 rounded-full opacity-70"
                animate={{
                  y: [0, 20, 0],
                  scale: [1, 1.3, 1],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{
                  duration: 3.5,
                  repeat: Infinity,
                  delay: 1
                }}
              />
              <motion.div
                className="absolute top-1/2 -left-12 w-5 h-5 bg-purple-400 rounded-full opacity-70"
                animate={{
                  x: [0, -15, 0],
                  scale: [1, 1.1, 1],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  delay: 2
                }}
              />
              <motion.div
                className="absolute top-1/3 -right-12 w-7 h-7 bg-yellow-400 rounded-full opacity-70"
                animate={{
                  x: [0, 15, 0],
                  scale: [1, 1.4, 1],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{
                  duration: 2.5,
                  repeat: Infinity,
                  delay: 3
                }}
              />
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default Hero
